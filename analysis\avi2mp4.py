#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AVI to MP4 Converter with H.265 Encoding
将AVI视频文件转换为H.265编码的MP4文件

Usage:
    python avi2mp4.py input_directory output_directory
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import logging

# 配置参数
CONFIG = {
    'video_codec': 'libx265',  # H.265编码器
    'crf': 23,  # 质量参数 (18-28, 越小质量越好)
    'preset': 'medium',  # 编码速度预设 (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
    'audio_codec': 'aac',  # 音频编码器
    'audio_bitrate': '128k',  # 音频比特率
    'log_level': 'INFO'
}

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, CONFIG['log_level']),
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('avi2mp4_conversion.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def check_ffmpeg():
    """检查ffmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def find_avi_files(input_dir):
    """查找目录下所有AVI文件"""
    input_path = Path(input_dir)
    if not input_path.exists():
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")

    avi_files = []
    for root, dirs, files in os.walk(input_path):
        for file in files:
            if file.lower().endswith('.avi'):
                avi_files.append(Path(root) / file)

    return avi_files

def find_video_directories(root_dir):
    """查找所有名为'video'的子目录"""
    root_path = Path(root_dir)
    if not root_path.exists():
        raise FileNotFoundError(f"根目录不存在: {root_dir}")

    video_dirs = []
    for root, dirs, files in os.walk(root_path):
        for dir_name in dirs:
            if dir_name.lower() == 'video':
                video_dirs.append(Path(root) / dir_name)

    return video_dirs

def convert_avi_to_mp4(input_file, output_file, logger):
    """转换单个AVI文件为MP4"""
    try:
        # 确保输出目录存在
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 构建ffmpeg命令
        cmd = [
            'ffmpeg',
            '-i', str(input_file),
            '-c:v', CONFIG['video_codec'],
            '-crf', str(CONFIG['crf']),
            '-preset', CONFIG['preset'],
            '-c:a', CONFIG['audio_codec'],
            '-b:a', CONFIG['audio_bitrate'],
            '-y',  # 覆盖输出文件
            str(output_file)
        ]
        
        logger.info(f"开始转换: {input_file.name}")
        logger.debug(f"命令: {' '.join(cmd)}")
        
        # 执行转换
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        # 检查输出文件是否创建成功
        if output_file.exists() and output_file.stat().st_size > 0:
            input_size = input_file.stat().st_size / (1024*1024)  # MB
            output_size = output_file.stat().st_size / (1024*1024)  # MB
            compression_ratio = (1 - output_size/input_size) * 100
            
            logger.info(f"转换成功: {input_file.name}")
            logger.info(f"  原始大小: {input_size:.1f} MB")
            logger.info(f"  转换后大小: {output_size:.1f} MB")
            logger.info(f"  压缩率: {compression_ratio:.1f}%")
            return True
        else:
            logger.error(f"转换失败: 输出文件未创建或为空 - {output_file}")
            return False
            
    except subprocess.CalledProcessError as e:
        logger.error(f"转换失败: {input_file.name}")
        logger.error(f"错误信息: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"转换过程中发生错误: {input_file.name} - {str(e)}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将AVI视频文件转换为H.265编码的MP4文件')
    parser.add_argument('--input_dir', help='输入目录路径（单个video目录）', default=None)
    parser.add_argument('--output_dir', help='输出目录路径', default=None)
    parser.add_argument('--root_dir', help='根目录路径（搜索所有video子目录）', default=None)
    parser.add_argument('--crf', type=int, default=CONFIG['crf'],
                       help=f'质量参数 (18-28, 默认: {CONFIG["crf"]})')
    parser.add_argument('--preset', default=CONFIG['preset'],
                       choices=['ultrafast', 'superfast', 'veryfast', 'faster', 'fast', 'medium', 'slow', 'slower', 'veryslow'],
                       help=f'编码速度预设 (默认: {CONFIG["preset"]})')

    args = parser.parse_args()
    # args.root_dir = "C:/Users/<USER>/Desktop/curiosity_pupil/data"
    args.root_dir = "E:/"
    # args.input_dir = "C:/Users/<USER>/Desktop/curiosity_pupil/data/20250724_122823_zhuqingchao_continuous_reading/video"

    # 更新配置
    CONFIG['crf'] = args.crf
    CONFIG['preset'] = args.preset

    if not args.input_dir:
        print("请提供输入的视频路径上级目录")
    
    # 如果没有提供参数，使用默认值
    if not args.output_dir and not args.root_dir:
        args.output_dir = args.input_dir + "_mp4"
        os.makedirs(args.output_dir, exist_ok=True)

    # 设置日志
    logger = setup_logging()
    
    try:
        # 检查ffmpeg
        if not check_ffmpeg():
            logger.error("ffmpeg未找到，请确保已安装ffmpeg并添加到PATH环境变量")
            sys.exit(1)

        # 根据参数选择处理模式
        if args.root_dir:
            # 模式1: 搜索所有video子目录
            logger.info(f"搜索所有video子目录: {args.root_dir}")
            video_dirs = find_video_directories(args.root_dir)

            if not video_dirs:
                logger.warning(f"在目录 {args.root_dir} 中未找到名为'video'的子目录")
                return

            logger.info(f"找到 {len(video_dirs)} 个video目录")

            total_success = 0
            total_files = 0

            for video_dir in video_dirs:
                logger.info(f"处理video目录: {video_dir}")

                # 查找该video目录下的AVI文件
                avi_files = find_avi_files(video_dir)
                if not avi_files:
                    logger.info(f"  未找到AVI文件，跳过")
                    continue

                logger.info(f"  找到 {len(avi_files)} 个AVI文件")

                # 创建对应的输出目录 (video_mp4)
                output_dir = video_dir.parent / f"{video_dir.name}_mp4"
                output_dir.mkdir(exist_ok=True)

                # 转换文件
                success_count = 0
                for i, avi_file in enumerate(avi_files, 1):
                    relative_path = avi_file.relative_to(video_dir)
                    output_file = output_dir / relative_path.with_suffix('.mp4')

                    logger.info(f"  处理进度: {i}/{len(avi_files)} - {avi_file.name}")

                    if convert_avi_to_mp4(avi_file, output_file, logger):
                        success_count += 1

                total_success += success_count
                total_files += len(avi_files)

                logger.info(f"  该目录转换完成: {success_count}/{len(avi_files)} 个文件")

            # 输出总结
            logger.info(f"所有video目录转换完成!")
            logger.info(f"总计成功转换: {total_success}/{total_files} 个文件")

        else:
            # 模式2: 处理单个目录
            logger.info(f"搜索AVI文件: {args.input_dir}")
            avi_files = find_avi_files(args.input_dir)

            if not avi_files:
                logger.warning(f"在目录 {args.input_dir} 中未找到AVI文件")
                return

            logger.info(f"找到 {len(avi_files)} 个AVI文件")

            # 转换文件
            input_path = Path(args.input_dir)
            output_path = Path(args.output_dir)

            success_count = 0
            total_count = len(avi_files)

            for i, avi_file in enumerate(avi_files, 1):
                # 计算相对路径以保持目录结构
                relative_path = avi_file.relative_to(input_path)
                output_file = output_path / relative_path.with_suffix('.mp4')

                logger.info(f"处理进度: {i}/{total_count}")

                if convert_avi_to_mp4(avi_file, output_file, logger):
                    success_count += 1

            # 输出总结
            logger.info(f"转换完成!")
            logger.info(f"成功转换: {success_count}/{total_count} 个文件")

            if success_count < total_count:
                logger.warning(f"有 {total_count - success_count} 个文件转换失败，请检查日志")
        
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
